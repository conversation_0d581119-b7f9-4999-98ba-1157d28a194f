# موقع قراء القرآن الكريم | Quran Reciters Website

موقع ويب تفاعلي للاستماع إلى تلاوات القرآن الكريم من أشهر القراء حول العالم، مبني باستخدام Alpine.js و Tailwind CSS مع دعم كامل للغة العربية والتخطيط من اليمين إلى اليسار (RTL).

An interactive website to listen to beautiful Quran recitations from renowned reciters worldwide, built with Alpine.js and Tailwind CSS with full Arabic language and RTL layout support.

## ✨ المميزات | Features

### 🌐 دعم اللغة العربية | Arabic Language Support
- **واجهة عربية كاملة**: جميع النصوص والعناوين والأزرار باللغة العربية
- **أسماء القراء بالعربية**: عرض أسماء القراء بالخط العربي الأصيل
- **أسماء السور بالعربية**: عرض أسماء السور باللغة العربية مع الترجمة الإنجليزية
- **رسائل الخطأ بالعربية**: جميع رسائل النظام باللغة العربية

### 📱 التخطيط من اليمين إلى اليسار | RTL Layout
- **اتجاه النص**: دعم كامل لاتجاه النص من اليمين إلى اليسار
- **ترتيب العناصر**: ترتيب الأزرار والأيقونات بما يتناسب مع التخطيط العربي
- **التصميم المتجاوب**: يعمل بشكل مثالي على جميع الأجهزة مع الحفاظ على التخطيط العربي

### 🎨 الخطوط العربية | Arabic Typography
- **خط القاهرة**: للنصوص العامة والواجهة
- **خط الأميري**: للنصوص العربية الكلاسيكية (أسماء السور والقراء)
- **وضوح القراءة**: خطوط محسنة لضمان وضوح النصوص العربية والإنجليزية

### 🎵 مميزات الصوت | Audio Features
- **30 قارئ مشهور**: مجموعة من أشهر قراء القرآن الكريم
- **114 سورة**: جميع سور القرآن الكريم متاحة لكل قارئ
- **تشغيل مباشر**: إمكانية الاستماع المباشر للتلاوات
- **تحميل الملفات**: تحميل التلاوات بأسماء عربية
- **مشغل صوتي متقدم**: شريط تقدم وأزرار تحكم

## 🚀 كيفية الاستخدام | How to Use

### تشغيل الموقع | Running the Website
```bash
# تشغيل خادم محلي
python3 -m http.server 8000

# أو باستخدام Node.js
npx serve .

# ثم افتح المتصفح على
http://localhost:8000
```

### التنقل في الموقع | Website Navigation
1. **الصفحة الرئيسية**: تصفح 30 قارئ مشهور
2. **صفحة القارئ**: اختر أي قارئ لعرض جميع السور
3. **تشغيل الصوت**: اضغط على زر التشغيل ▶️ للاستماع
4. **تحميل الملف**: اضغط على زر التحميل ⬇️ لحفظ التلاوة

## 🛠️ التقنيات المستخدمة | Technologies Used

- **Alpine.js**: للتفاعل والحالة التفاعلية
- **Tailwind CSS**: للتصميم والتخطيط المتجاوب
- **Font Awesome**: للأيقونات
- **Google Fonts**: للخطوط العربية (Cairo & Amiri)
- **mp3quran.net API**: لبيانات القراء والتلاوات الصوتية عالية الجودة

## 📁 هيكل المشروع | Project Structure

```
reciters/
├── index.html          # الصفحة الرئيسية - قائمة القراء
├── reciter.html        # صفحة القارئ - قائمة السور
└── README.md          # ملف التوثيق
```

## 🎯 المميزات التقنية | Technical Features

### دعم RTL المتقدم | Advanced RTL Support
- `dir="rtl"` و `lang="ar"` في HTML
- استخدام `space-x-reverse` لترتيب العناصر
- تعديل `margin` و `padding` للاتجاه الصحيح
- أيقونات معكوسة (arrow-right بدلاً من arrow-left)

### إدارة البيانات | Data Management
- أسماء القراء بالعربية والإنجليزية
- أسماء البلدان بالعربية
- معلومات السور من API mp3quran.net
- ترجمة أنواع الوحي (مكية/مدنية)
- دعم مصاحف متعددة لكل قارئ
- جودة صوتية عالية من خوادم mp3quran.net

### تجربة المستخدم | User Experience
- رسائل تحميل بالعربية
- رسائل خطأ مترجمة
- أسماء ملفات التحميل بالعربية
- واجهة سهلة الاستخدام للمتحدثين بالعربية

## 🌟 القراء المتاحون | Available Reciters

الموقع يضم 30 قارئ مشهور من مختلف البلدان العربية والإسلامية، منهم:
- مشاري راشد العفاسي (الكويت)
- عبد الرحمن السديس (السعودية)
- أبو بكر الشاطري (السعودية)
- ماهر المعيقلي (السعودية)
- وغيرهم الكثير...

## 📱 التوافق | Compatibility

- ✅ جميع المتصفحات الحديثة
- ✅ الهواتف الذكية والأجهزة اللوحية
- ✅ أجهزة سطح المكتب
- ✅ دعم كامل للغة العربية في جميع البيئات

## 🤝 المساهمة | Contributing

نرحب بالمساهمات لتحسين الموقع:
- إضافة قراء جدد
- تحسين الترجمات العربية
- تطوير المميزات الجديدة
- إصلاح الأخطاء

---

**بُني بحب للمجتمع الإسلامي** ❤️ | **Made with love for the Muslim community** ❤️
