<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قراء القرآن الكريم - استمع إلى التلاوات الجميلة</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@400;600;700&display=swap"
        rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', '<PERSON>i', sans-serif;
        }

        .arabic-text {
            font-family: 'Cairo', serif;
        }

        /* Custom animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-fade-in-up {
            animation: fadeInUp 0.6s ease-out forwards;
            opacity: 0;
        }
    </style>
</head>

<body class="bg-gradient-to-bl from-green-50 to-blue-50 min-h-screen">
    <div x-data="recitersApp()" class="container mx-auto px-4 py-8">
        <!-- Header -->
        <header class="text-center mb-12">
            <h1 class="text-4xl md:text-6xl font-bold text-green-800 mb-4 arabic-text">
                <i class="fas fa-mosque text-green-600 ml-4"></i>
                قراء القرآن الكريم
            </h1>
            <p class="text-lg text-gray-600 max-w-2xl mx-auto mb-8">
                استمع إلى التلاوات الجميلة للقرآن الكريم من قراء مشهورين حول العالم
            </p>

            <!-- Search Section -->
            <div class="max-w-md mx-auto mb-8">
                <div class="relative">
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input type="text" x-model="searchQuery" @input.debounce.300ms="filterReciters()"
                        placeholder="ابحث عن قارئ... (مثال: مشاري العفاسي، الكويت)"
                        class="w-full pl-10 pr-10 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent text-right arabic-text bg-white shadow-md"
                        dir="rtl">
                    <button x-show="searchQuery.length > 0" @click="clearSearch()"
                        class="absolute inset-y-0 left-0 pl-3 flex items-center cursor-pointer text-gray-400 hover:text-gray-600 transition-colors">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <!-- Search Results Count -->
                <div x-show="searchQuery.length > 0" class="mt-2 text-sm text-gray-600">
                    <span x-text="filteredReciters.length"></span> قارئ من أصل <span x-text="reciters.length"></span>
                </div>
            </div>
        </header>

        <!-- Loading State -->
        <div x-show="loading" class="flex justify-center items-center py-20">
            <div class="animate-spin rounded-full h-16 w-16 border-b-2 border-green-600"></div>
            <p class="mr-4 text-gray-600">جاري التحميل...</p>
        </div>

        <!-- No Results Message -->
        <div x-show="!loading && filteredReciters.length === 0 && searchQuery.length > 0" class="text-center py-16">
            <div class="max-w-md mx-auto">
                <i class="fas fa-search text-gray-300 text-6xl mb-4"></i>
                <h3 class="text-2xl font-bold text-gray-600 mb-2 arabic-text">لا توجد نتائج</h3>
                <p class="text-gray-500 mb-6">
                    لم نجد أي قارئ يطابق بحثك عن "<span x-text="searchQuery" class="font-semibold"></span>"
                </p>
                <button @click="clearSearch()"
                    class="bg-gradient-to-r from-green-500 to-blue-500 text-white py-2 px-6 rounded-lg hover:from-green-600 hover:to-blue-600 transition-all duration-200 font-semibold">
                    <i class="fas fa-times ml-2"></i>
                    مسح البحث
                </button>
            </div>
        </div>

        <!-- Reciters Grid -->
        <div x-show="!loading && filteredReciters.length > 0"
            class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            <template x-for="(reciter, index) in filteredReciters" :key="reciter.id">
                <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 cursor-pointer animate-fade-in-up"
                    :style="`animation-delay: ${index * 0.1}s`" @click="goToReciter(reciter)">
                    <div class="p-6 text-center">
                        <!-- Reciter Image -->
                        <div
                            class="w-24 h-24 mx-auto mb-4 rounded-full bg-gradient-to-r from-green-400 to-blue-500 flex items-center justify-center">
                            <i class="fas fa-user text-white text-3xl" x-show="!reciter.image"></i>
                            <img x-show="reciter.image" :src="reciter.image" :alt="reciter.name"
                                class="w-full h-full rounded-full object-cover">
                        </div>

                        <!-- Reciter Name -->
                        <h3 class="text-xl font-semibold text-gray-800 mb-2 arabic-text" x-text="reciter.arabicName">
                        </h3>
                        <p class="text-sm text-gray-600 mb-1" x-text="reciter.name"></p>
                        <p class="text-sm text-gray-500 mb-4" x-text="reciter.country"></p>

                        <!-- Stats -->
                        <div class="flex justify-center space-x-reverse space-x-4 text-sm text-gray-600">
                            <span><i class="fas fa-book-open ml-1"></i>١١٤ سورة</span>
                        </div>

                        <!-- Action Button -->
                        <button
                            class="mt-4 bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-full transition-colors duration-200">
                            <i class="fas fa-play ml-2"></i>استمع
                        </button>
                    </div>
                </div>
            </template>
        </div>

        <!-- Footer -->
        <footer class="text-center mt-16 py-8 border-t border-gray-200">
            <p class="text-gray-600">
                <i class="fas fa-heart text-red-500 ml-1"></i>
                صُنع بحب للمجتمع الإسلامي
            </p>
        </footer>
    </div>

    <script>
        function recitersApp() {
            return {
                loading: true,
                reciters: [],
                filteredReciters: [],
                searchQuery: '',

                init() {
                    this.loadReciters();
                },

                async loadReciters() {
                    try {
                        // Load reciters from mp3quran.net API
                        const response = await fetch('https://mp3quran.net/api/v3/reciters?language=ar');
                        const data = await response.json();

                        if (data.reciters) {
                            // Select popular reciters and transform data
                            const popularReciterIds = [123, 102, 60, 86, 89, 92, 118, 112, 111, 109, 106, 13, 8, 6, 76, 87, 88, 96, 97, 74, 72, 71, 69, 67, 66, 62, 61, 59, 58, 57];

                            this.reciters = data.reciters
                                .filter(reciter => popularReciterIds.includes(reciter.id))
                                .slice(0, 30)
                                .map(reciter => ({
                                    id: reciter.id,
                                    name: this.getEnglishName(reciter.name),
                                    arabicName: reciter.name,
                                    country: this.getCountryFromName(reciter.name),
                                    image: null,
                                    moshaf: reciter.moshaf
                                }));
                        }

                        // Initialize filtered reciters with all reciters
                        this.filteredReciters = [...this.reciters];
                        this.loading = false;
                    } catch (error) {
                        console.error('Error loading reciters:', error);
                        this.loading = false;
                    }
                },

                getEnglishName(arabicName) {
                    const nameMap = {
                        'مشاري العفاسي': 'Mishary Al-Afasy',
                        'ماهر المعيقلي': 'Maher Al-Muaiqly',
                        'عبد الله بصفر': 'Abdullah Basfar',
                        'ناصر القطامي': 'Nasser Al-Qatami',
                        'هاني الرفاعي': 'Hani Ar-Rifai',
                        'ياسر الدوسري': 'Yasser Al-Dosari',
                        'محمود خليل الحصري': 'Mahmoud Khalil Al-Husary',
                        'محمد صديق المنشاوي': 'Mohamed Siddiq El-Minshawi',
                        'محمد جبريل': 'Muhammad Jibreel',
                        'محمد أيوب': 'Muhammad Ayyub',
                        'محمد الطبلاوي': 'Muhammad At-Tablawi',
                        'الزين محمد أحمد': 'Alzain Mohammad Ahmad',
                        'أحمد صابر': 'Ahmad Saber',
                        'أحمد الحواشي': 'Ahmad Al-Hawashi',
                        'علي جابر': 'Ali Jaber',
                        'نبيل الرفاعي': 'Nabil Ar-Rifai',
                        'نعمة الحسان': 'Ni\'mah Al-Hassan',
                        'يحيى حوا': 'Yahya Hawwa',
                        'يوسف الشويعي': 'Yusuf Ash-Shuway\'i',
                        'علي بن عبدالرحمن الحذيفي': 'Ali Al-Hudhaifi',
                        'عبدالولي الأركاني': 'Abdul Wali Al-Arkani',
                        'عبدالودود حنيف': 'Abdul Wadud Hanif',
                        'عبدالمحسن العبيكان': 'Abdul Mohsen Al-Obaykan',
                        'عبدالمحسن القاسم': 'Abdul Mohsen Al-Qasim',
                        'عبدالمحسن الحارثي': 'Abdul Mohsen Al-Harthi',
                        'عبدالله عواد الجهني': 'Abdullah Awad Al-Juhani',
                        'عبدالله خياط': 'Abdullah Khayat',
                        'عبدالله المطرود': 'Abdullah Al-Matrood',
                        'عبدالله البعيجان': 'Abdullah Al-Buaijan',
                        'عبدالله غيلان': 'Abdullah Ghaylan'
                    };

                    // Try to find exact match first
                    for (const [arabic, english] of Object.entries(nameMap)) {
                        if (arabicName.includes(arabic)) {
                            return english;
                        }
                    }

                    // Return transliterated version if no match found
                    return arabicName;
                },

                getCountryFromName(arabicName) {
                    // Most reciters are from Saudi Arabia, with some from Egypt, Kuwait, etc.
                    if (arabicName.includes('الحصري') || arabicName.includes('المنشاوي') || arabicName.includes('الطبلاوي')) {
                        return 'مصر';
                    } else if (arabicName.includes('العفاسي')) {
                        return 'الكويت';
                    } else {
                        return 'السعودية';
                    }
                },

                goToReciter(reciter) {
                    // Navigate to reciter page with URL parameters
                    const params = new URLSearchParams({
                        id: reciter.id,
                        name: reciter.name,
                        arabicName: reciter.arabicName,
                        country: reciter.country
                    });
                    window.location.href = `reciter.html?${params.toString()}`;
                },

                filterReciters() {
                    if (!this.searchQuery.trim()) {
                        this.filteredReciters = [...this.reciters];
                        return;
                    }

                    const query = this.normalizeArabicText(this.searchQuery.toLowerCase());

                    this.filteredReciters = this.reciters.filter(reciter => {
                        const arabicName = this.normalizeArabicText(reciter.arabicName.toLowerCase());
                        const englishName = reciter.name.toLowerCase();
                        const country = this.normalizeArabicText(reciter.country.toLowerCase());

                        return arabicName.includes(query) ||
                            englishName.includes(query) ||
                            country.includes(query);
                    });
                },

                normalizeArabicText(text) {
                    // Normalize Arabic text by removing diacritics and standardizing characters
                    return text
                        .replace(/[ًٌٍَُِّْ]/g, '') // Remove diacritics
                        .replace(/[أإآ]/g, 'ا')      // Normalize alif
                        .replace(/[ةه]/g, 'ه')       // Normalize teh marbuta
                        .replace(/[ىي]/g, 'ي')       // Normalize yeh
                        .trim();
                },

                clearSearch() {
                    this.searchQuery = '';
                    this.filteredReciters = [...this.reciters];
                }
            }
        }
    </script>
</body>

</html>